"""
Google Gemini-powered receipt data extraction module
"""

import json
import re
from datetime import datetime
from typing import Dict, Optional, Any
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold

from config import config


class GeminiReceiptParser:
    """Receipt parser using Google Gemini AI models"""
    
    def __init__(self):
        self.model = None
        self._initialize_gemini()
    
    def _initialize_gemini(self):
        """Initialize Google Gemini API"""
        if not config.is_gemini_configured():
            print("⚠️  Google Gemini API key not configured. Using fallback parser.")
            return
        
        try:
            genai.configure(api_key=config.GOOGLE_GEMINI_API_KEY)
            
            # Configure the model
            generation_config = {
                "temperature": config.GEMINI_TEMPERATURE,
                "top_p": 0.95,
                "top_k": 64,
                "max_output_tokens": config.GEMINI_MAX_TOKENS,
                "response_mime_type": "application/json",
            }
            
            # Configure safety settings to be less restrictive for business documents
            safety_settings = {
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
            }
            
            self.model = genai.GenerativeModel(
                model_name=config.GEMINI_MODEL,
                generation_config=generation_config,
                safety_settings=safety_settings
            )
            
            print(f"✅ Google Gemini {config.GEMINI_MODEL} initialized successfully")
            
        except Exception as e:
            print(f"⚠️  Failed to initialize Google Gemini: {str(e)}")
            self.model = None
    
    def _create_extraction_prompt(self, text: str) -> str:
        """Create a detailed prompt for receipt data extraction"""
        return f"""
You are an expert receipt data extraction system. Extract structured information from the following receipt text and return it as valid JSON.

Receipt Text:
{text}

Extract the following information and return as JSON with these exact field names:
{{
    "merchant_name": "string - The business/store name (usually at the top)",
    "total_amount": "number - The final total amount paid (as float, no currency symbols)",
    "tax_amount": "number - Tax amount (as float, no currency symbols, null if not found)",
    "subtotal": "number - Subtotal before tax (as float, no currency symbols, null if not found)",
    "purchased_at": "string - Date in YYYY-MM-DD format (null if not found)",
    "payment_method": "string - Payment method used (cash, credit, debit, etc., null if not found)",
    "receipt_number": "string - Receipt or transaction number (null if not found)",
    "store_address": "string - Store address (null if not found)",
    "store_phone": "string - Store phone number (null if not found)",
    "items": [
        {{
            "name": "string - Item name",
            "quantity": "number - Quantity purchased",
            "unit_price": "number - Price per unit",
            "total_price": "number - Total price for this item"
        }}
    ]
}}

Rules:
1. Return ONLY valid JSON, no additional text or explanations
2. Use null for missing values, not empty strings
3. Extract amounts as numbers without currency symbols ($, €, etc.)
4. For dates, convert to YYYY-MM-DD format if possible
5. Be conservative - if you're not confident about a value, use null
6. For merchant_name, look for the business name usually at the top of the receipt
7. For total_amount, look for "Total", "Amount Due", "Grand Total", etc.
8. Extract individual items if clearly listed with prices
"""
    
    def parse_receipt_with_gemini(self, text: str) -> Dict:
        """Parse receipt text using Google Gemini"""
        if not self.model:
            raise Exception("Gemini model not initialized")
        
        try:
            prompt = self._create_extraction_prompt(text)
            response = self.model.generate_content(prompt)
            
            if not response.text:
                raise Exception("Empty response from Gemini")
            
            # Parse the JSON response
            parsed_data = json.loads(response.text)
            
            # Validate and clean the data
            cleaned_data = self._validate_and_clean_data(parsed_data, text)
            
            print(f"✅ Gemini successfully extracted receipt data")
            return cleaned_data
            
        except json.JSONDecodeError as e:
            print(f"⚠️  Failed to parse Gemini JSON response: {str(e)}")
            print(f"Raw response: {response.text if 'response' in locals() else 'No response'}")
            raise Exception(f"Invalid JSON response from Gemini: {str(e)}")
            
        except Exception as e:
            print(f"⚠️  Gemini parsing failed: {str(e)}")
            raise Exception(f"Gemini parsing error: {str(e)}")
    
    def _validate_and_clean_data(self, data: Dict, original_text: str) -> Dict:
        """Validate and clean the extracted data"""
        cleaned = {
            'merchant_name': self._clean_string(data.get('merchant_name')),
            'total_amount': self._clean_amount(data.get('total_amount')),
            'tax_amount': self._clean_amount(data.get('tax_amount')),
            'subtotal': self._clean_amount(data.get('subtotal')),
            'purchased_at': self._clean_date(data.get('purchased_at')),
            'payment_method': self._clean_string(data.get('payment_method')),
            'receipt_number': self._clean_string(data.get('receipt_number')),
            'store_address': self._clean_string(data.get('store_address')),
            'store_phone': self._clean_string(data.get('store_phone')),
            'items': self._clean_items(data.get('items', [])),
            'raw_text': original_text,
            'extraction_method': 'gemini'
        }
        
        return cleaned
    
    def _clean_string(self, value: Any) -> Optional[str]:
        """Clean and validate string values"""
        if value is None or value == "":
            return None
        if isinstance(value, str):
            cleaned = value.strip()
            return cleaned if cleaned else None
        return str(value).strip() if str(value).strip() else None
    
    def _clean_amount(self, value: Any) -> Optional[float]:
        """Clean and validate monetary amounts"""
        if value is None:
            return None
        
        try:
            if isinstance(value, (int, float)):
                return float(value) if value >= 0 else None
            
            if isinstance(value, str):
                # Remove currency symbols and clean
                cleaned = re.sub(r'[^\d.-]', '', value.strip())
                if cleaned:
                    amount = float(cleaned)
                    return amount if amount >= 0 else None
        except (ValueError, TypeError):
            pass
        
        return None
    
    def _clean_date(self, value: Any) -> Optional[datetime]:
        """Clean and validate date values"""
        if value is None:
            return None
        
        if isinstance(value, str):
            # Try to parse various date formats
            date_formats = [
                '%Y-%m-%d',
                '%m/%d/%Y',
                '%d/%m/%Y',
                '%Y/%m/%d',
                '%m-%d-%Y',
                '%d-%m-%Y'
            ]
            
            for fmt in date_formats:
                try:
                    return datetime.strptime(value.strip(), fmt)
                except ValueError:
                    continue
        
        return None
    
    def _clean_items(self, items: Any) -> list:
        """Clean and validate items list"""
        if not isinstance(items, list):
            return []
        
        cleaned_items = []
        for item in items:
            if isinstance(item, dict):
                cleaned_item = {
                    'name': self._clean_string(item.get('name')),
                    'quantity': self._clean_amount(item.get('quantity')),
                    'unit_price': self._clean_amount(item.get('unit_price')),
                    'total_price': self._clean_amount(item.get('total_price'))
                }
                
                # Only include items with at least a name
                if cleaned_item['name']:
                    cleaned_items.append(cleaned_item)
        
        return cleaned_items
    
    def is_available(self) -> bool:
        """Check if Gemini parser is available"""
        return self.model is not None


# Global parser instance
gemini_parser = GeminiReceiptParser()
